<?php
/**
 * Test script to verify the background extractor is working
 */

echo "=== Testing Background Extractor ===\n\n";

// Create a test file
$testContent = "This is a test document.\n\nIt contains multiple lines of text.\n\nThis should be extracted by Gemini AI.";
$testFilePath = __DIR__ . '/test_document.txt';
file_put_contents($testFilePath, $testContent);

echo "Created test file: {$testFilePath}\n";
echo "File size: " . filesize($testFilePath) . " bytes\n";
echo "Content preview: " . substr($testContent, 0, 50) . "...\n\n";

// Test the background extractor
$processId = 'test_' . uniqid();
echo "Testing background extractor with process ID: {$processId}\n\n";

// Run the background extractor
$extractorPath = __DIR__ . '/public/background_extractor.php';
$command = "php \"{$extractorPath}\" \"{$processId}\" \"{$testFilePath}\"";

echo "Running command: {$command}\n";
echo "=== Background Extractor Output ===\n";

// Execute and capture output
$output = shell_exec($command . ' 2>&1');
echo $output;

echo "\n=== Test Complete ===\n";

// Clean up
if (file_exists($testFilePath)) {
    unlink($testFilePath);
    echo "Test file cleaned up.\n";
}
?>
