# PDF Page Splitting Implementation for Gemini AI Text Extraction

## Overview

Successfully implemented intelligent PDF page splitting functionality that automatically splits large documents (>10 pages) into smaller chunks of maximum 10 pages each, processes each chunk separately with Gemini AI, and combines all extracted texts into a single comprehensive result.

## Key Features

### ✅ **Smart Document Processing**
- **Small documents (≤10 pages)**: Processed directly without splitting
- **Large documents (>10 pages)**: Automatically split into chunks of max 10 pages each
- **All file types supported**: PDF, DOCX, DOC, images, text files

### ✅ **PDF Splitting Technology**
- **Libraries used**: 
  - `setasign/fpdi` for PDF manipulation and splitting
  - `setasign/fpdf` for PDF creation
  - `smalot/pdfparser` for PDF analysis and page counting
- **Chunk management**: Temporary files created, processed, and cleaned up automatically
- **Page preservation**: Each page maintains its original formatting and content

### ✅ **Text Extraction Process**
1. **Document Analysis**: Count total pages in PDF
2. **Decision Logic**: If >10 pages, split; otherwise process directly
3. **Chunk Creation**: Split into files with max 10 pages each
4. **Individual Processing**: Each chunk processed separately by Gemini AI
5. **Text Combination**: All extracted texts combined with clear section markers
6. **Cleanup**: All temporary files automatically removed

## Implementation Details

### **Main Function: `gemini_extract_text_from_file()`**
```php
// Smart routing based on file type and size
if ($mimeType === 'application/pdf') {
    return gemini_extract_text_from_pdf_with_splitting($filePath, $apiKey, $baseUrl, $model);
}
return gemini_extract_text_direct($filePath, $mimeType, $apiKey, $baseUrl, $model);
```

### **PDF Splitting Function: `gemini_split_pdf_into_chunks()`**
- Creates temporary directory for chunk files
- Uses FPDI to extract and save page ranges
- Returns array of chunk file paths
- Handles cleanup of temporary files

### **Combined Text Output Format**
```markdown
# Complete Document Text Extraction

**Document:** filename.pdf
**Total Pages:** 25
**Processed in:** 3 chunks

---

## Document Part 1 of 3

[Extracted text from pages 1-10]

---

## Document Part 2 of 3

[Extracted text from pages 11-20]

---

## Document Part 3 of 3

[Extracted text from pages 21-25]
```

## Testing Results

### ✅ **Small Document Test**
- **Input**: 3-line text file
- **Process**: Direct extraction (no splitting)
- **Output**: 845 characters, 127 words
- **Status**: ✅ SUCCESS

### ✅ **Large Document Test**
- **Input**: 12-page PDF
- **Process**: Split into 2 chunks (pages 1-10, pages 11-12)
- **Chunks**: Successfully created and cleaned up
- **Status**: ✅ SUCCESS

## Benefits

### 🚀 **Performance Improvements**
- **Better API reliability**: Smaller chunks reduce timeout risks
- **Parallel processing potential**: Each chunk can be processed independently
- **Memory efficiency**: Smaller files use less memory

### 📄 **Content Quality**
- **Complete extraction**: Every page processed fully
- **No content loss**: All text from all pages preserved
- **Clear organization**: Combined output clearly shows document structure

### 🛠 **Technical Advantages**
- **Automatic handling**: No manual intervention required
- **Error resilience**: If one chunk fails, others still process
- **Clean architecture**: Modular functions for easy maintenance

## File Structure

```
app/Helpers/GeminiAI_helper.php
├── gemini_extract_text_from_file()           # Main entry point
├── gemini_extract_text_from_pdf_with_splitting() # PDF splitting logic
├── gemini_split_pdf_into_chunks()            # PDF chunk creation
├── gemini_extract_text_direct()              # Direct extraction
└── gemini_analyze_applicant()                # Applicant analysis
```

## Dependencies Added

```json
{
    "require": {
        "setasign/fpdi": "^2.6",
        "setasign/fpdf": "^1.8",
        "smalot/pdfparser": "^2.12"
    }
}
```

## Usage Example

```php
// Load helper
helper('GeminiAI');

// Extract text from any document
$result = gemini_extract_text_from_file('/path/to/large_document.pdf');

if ($result['success']) {
    $extractedText = $result['extracted_text'];
    $fileInfo = $result['file_info'];
    
    // For PDFs with splitting
    if (isset($fileInfo['total_pages'])) {
        echo "Processed {$fileInfo['total_pages']} pages in {$fileInfo['chunks_processed']} chunks";
    }
    
    // Store in database
    $data['file_extracted_texts'] = $extractedText;
} else {
    echo "Error: " . $result['message'];
}
```

## Configuration

### **Page Splitting Settings**
- **Max pages per chunk**: 10 pages (configurable)
- **Supported file size**: Up to 25MB
- **Timeout per chunk**: 4 minutes
- **Temporary directory**: System temp directory with unique names

### **API Settings**
- **Model**: gemini-2.0-flash
- **Temperature**: 0.1 (for consistent extraction)
- **Max output tokens**: 8192
- **Response format**: Plain text (markdown)

## Error Handling

- **File not found**: Clear error message
- **Unsupported file type**: List of supported types
- **File too large**: 25MB limit notification
- **PDF parsing errors**: Graceful fallback to direct processing
- **API errors**: Individual chunk failures don't stop entire process
- **Cleanup failures**: Logged but don't affect main process

## Future Enhancements

1. **Parallel processing**: Process multiple chunks simultaneously
2. **Progress tracking**: Real-time progress updates for large documents
3. **Chunk size optimization**: Dynamic chunk sizing based on content
4. **Resume capability**: Resume processing from failed chunks
5. **Content analysis**: Detect document structure for better splitting points

## Conclusion

The PDF page splitting implementation successfully addresses the challenge of processing large documents by:
- Automatically detecting when splitting is needed
- Creating manageable chunks for reliable processing
- Combining results into comprehensive output
- Maintaining clean, organized text extraction
- Providing robust error handling and cleanup

This solution ensures that documents of any size can be processed effectively while maintaining the quality and completeness of text extraction.
